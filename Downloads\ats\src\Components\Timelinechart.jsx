import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

// Default function for onPointClick to prevent errors
const defaultOnPointClick = () => {};

const LearningAttitudeChart = ({
  Learningattitude,
  onPointClick = defaultOnPointClick,
}) => {
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    if (!Learningattitude || !Array.isArray(Learningattitude)) return;

    const transformedData = Learningattitude.map(({ company, count, skills }) => ({
      name: company,
      skillCount: count,
      skills: skills,
    }));

    setChartData(transformedData);
  }, [Learningattitude]);

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const { name, skillCount, skills } = payload[0].payload;
      return (
        <div
          className="custom-tooltip"
          style={{
            backgroundColor: "#fff",
            padding: "10px",
            border: "1px solid #ccc",
            maxWidth: "300px",
          }}
        >
          <p
            className="label"
            style={{ textAlign: "left", color: "#000" }}
          >
            Company: <strong style={{ fontSize: "14px" }}>{name}</strong>
          </p>
          <p
            className="intro"
            style={{ textAlign: "left", color: "#000", fontSize: "14px" }}
          >
            Skill Count: {skillCount}
          </p>
          <p
            className="desc"
            style={{ textAlign: "left", color: "#000", fontSize: "14px" }}
          >
            Skills: {skills}
          </p>
        </div>
      );
    }

    return null;
  };

  const renderCustomAxisTick = ({ x, y, payload }) => {
    const words = payload.value.split(" ");
    const lines = [];
    let currentLine = [];

    words.forEach((word) => {
      if (currentLine.join(" ").length + word.length > 15) {
        lines.push(currentLine.join(" "));
        currentLine = [word];
      } else {
        currentLine.push(word);
      }
    });

    if (currentLine.length) {
      lines.push(currentLine.join(" "));
    }

    return (
      <g transform={`translate(${x},${y}) rotate(-0)`}>
        <text fill="#32406d" textAnchor="middle">
          {lines.map((line, index) => (
            <tspan x={10} dy={index === 0 ? 0 : 20} key={index}>
              {line}
            </tspan>
          ))}
        </text>
      </g>
    );
  };

  return (
    <div style={{ backgroundColor: "#fff", paddingTop: "10px" }}>
      <div style={{ width: "100%", height: "100%" }}>
        <div
          style={{
            overflowX: "auto", // Enable horizontal scrolling
            paddingBottom: "20px", // Space for the scrollbar
          }}
        >
          <ResponsiveContainer
            width={chartData.length * 280} // Adjust width dynamically
            height={450}
          >
            <LineChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 50,
                bottom: 10,
              }}
              onClick={(e) => {
                if (e && e.activeLabel) {
                  const clickedData = chartData.find(
                    (d) => d.name === e.activeLabel
                  );
                  onPointClick(clickedData);
                }
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="name"
                padding={{ left: 10, right: 0 }}
                interval={0}
                angle={0}
                tick={renderCustomAxisTick}
                textAnchor="middle"
                height={90}
                label={{
                  value: "Company",
                  position: "insideBottom",
                  offset: 20,
                  style: {
                    fill: "rgb(136, 132, 216)",
                    fontSize: "20px",
                    fontWeight: "500",
                  },
                }}
                tickMargin={20}
              />
              <YAxis
                label={{
                  value: "Skills Count",
                  angle: -90,
                  position: "insideLeft",
                  offset: 10,
                  style: {
                    fill: "rgb(136, 132, 216)",
                    fontSize: "20px",
                    fontWeight: "500",
                  },
                  dy: 50,
                }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                connectNulls
                type="monotone"
                fill="#8884d8"
                dataKey="skillCount"
                stroke="#8884d8"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default LearningAttitudeChart;
