import React, { useState, useEffect } from "react";
 
import "./verfiy.css";
import { useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { ThreeDots } from "react-loader-spinner";
function Verify() {
  // const { username } = useParams();
  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const [name, setname] = useState("");
  useEffect(() => {
    // Extract the username from the URL query parameter
    const urlParams = new URLSearchParams(window.location.search);
    const user = urlParams.get("name");
 
    // Set the username if it exists
    if (user) {
      setname(user);
    } else {
      toast.error("Invalid or missing username in the link.");
    }
  }, []);
 
console.log(name,"theusername")
  const handleSubmittarget = async (e) => {
    e.preventDefault();
   
    if (!waitForSubmission) {
      setwaitForSubmission(true);
    // Prepare the payload for backend
    //console.log(username,"selectedrecruter")
    const payload = {
      name,
    };
    console.log("Payload being sent:", payload);
    try {
      const response = await fetch(
        "  http://192.168.0.47:5002/verify_user",
        // "  http://192.168.0.47:5002/login/management",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        },
      );
      if (response.ok) {
        const responsedata = await response.json();
       
        // Display success message
        toast.success(responsedata.message);
 
        // Check if status is success and redirect to the URL
        if (responsedata.status === "success" && responsedata.redirect_url) {
          // Redirect to the specified URL
          setTimeout(() => {           window.location.href = responsedata.redirect_url;         }, 3000);
        }
      } else {
        setwaitForSubmission(false);
        toast.error("Verification failed. Please try again.");
      }
     
    } catch (error) {
      setwaitForSubmission(false);
      // Handle any errors (e.g., show error message)
      console.error("Error submitting the form:", error);
    }
  }
  };
 
 
 
  return (
<div className="containerverfiy" style={{background: "linear-gradient(90deg, #C7C5F4, #776BCC)" }}>
  <div className="screen">
    <div className="screen__content">
 
      <form className="login" onSubmit={handleSubmittarget}>
        <div className="login__field">
          <i className="login__icon fas fa-user"></i>
          <input type="text" className="login__input"
               value={name}
               disabled
               required/>
        </div>
     
        <button className=" login__submit">
          <span className="button__text">Verfiy Now</span>
          <i className="button__icon fas fa-chevron-right"></i>
        </button>      
      </form>
     
    </div>
    <div className="screen__background">
      <span className="screen__background__shape screen__background__shape4"></span>
      <span className="screen__background__shape screen__background__shape3"></span>    
      <span className="screen__background__shape screen__background__shape2"></span>
      <span className="screen__background__shape screen__background__shape1"></span>
    </div>    
  </div>
</div>
 
  );
}
 
export default Verify;
 
 