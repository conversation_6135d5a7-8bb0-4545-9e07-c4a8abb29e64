//  25th july updatecode
import React, { useState, useEffect } from "react";
import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
import "../Dashboard/UpdateCandidate.css";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLocation, useNavigate } from "react-router-dom";
import { setDashboardData } from "../../store/slices/dashboardSlice";
import { FaArrowLeft } from "react-icons/fa";
import { FaAngleDown } from "react-icons/fa";
import { FaAngleRight } from "react-icons/fa";
import { useSelector, useDispatch } from "react-redux";
import { ThreeDots } from "react-loader-spinner";
import { store } from "../../store/store";
import { getDashboardData } from "../utilities.js";

// Initialize toast notifications
//toast.configure();

// const statusToLevelMapping = {
//   "screening selected": "Screening",
//   "screen rejected": "Screening",
//   "Sent To Peer Review": "PeerReview",
//   "L1 - schedule": "Level1",
//   "L1 - feedback": "Level1",
//   "L1 - selected": "Level1",
//   "L1 - rejected": "Level1",
//   "L1 - candidate reschedule": "Level1",
//   "L1 - panel reschedule": "Level1",
//   "L2 - schedule": "Level2",
//   "L2 - feedback": "Level2",
//   "L2 - selected": "Level2",
//   "L2 - rejected": "Level2",
//   "L2 - candidate reschedule": "Level2",
//   "L2 - panel reschedule": "Level2",
//   "L3 - schedule": "Level3",
//   "L3 - feedback": "Level3",
//   "L3 - selected": "Level3",
//   "L3 - rejected": "Level3",
//   "L3 - candidate reschedule": "Level3",
//   "L3 - panel reschedule": "Level3",
//   "hr - round": "Additional Rounds",
//   "managerial round": "Additional Rounds",
//   negotiation: "Final Outcomes",
//   selected: "Final Outcomes",
//   "offer - rejected": "Final Outcomes",
//   "offer - declined": "Final Outcomes",
//   "on - boarded": "Final Outcomes",
//   hold: "Other Status",
//   drop: "Other Status",
//   duplicate: "Other Status",
//   "candidate no-show": "Other Status",
// };

const statusToLevelMapping = {
  "Internal Screening": "Internal Screening",
  "Internal Screening Reject": "Internal Screening Reject",
  "Peer Review": "Peer Review",
  "Client Screening": "Client Screening",
  "Client Screening Reject": "Client Screening Reject",
  "Interview Stage": "Internal",
  "Interview Stage": "Client",
  "offer Stage": "Offer Stage",
  "Yet to Join": "Yet to Join",
  "Onboarded": "Onboarded",
  "Candidate Dropped": "Candidate Dropped",
  "Candidate Offer Declined": "Candidate Offer Declined",
  "Duplicate Profile": "Client",
  "Position on Hold": "Position on Hold",
  "Candidate on Hold": "Candidate on Hold",
  "Tech Reject": "Client",
  "Tech Reject": "Internal",
};




function UpdateCandidate() {
  const [option_selected, setOptionSelected] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const item = location.state.item || {};
  const [statusChanged, setStatusChanged] = useState(false);

  const [waitForSubmission, setwaitForSubmission] = useState(false);
  const [currentStatus, setCurrentStatus] = useState("");
  const [candidateStatus, setCandidateStatus] = useState("Select Level");
  const peerReviewer = item.peer_reviewer; // e.g., "teja"
  // const [comments, setComments] = useState(
  //   item.comments?.[peerReviewer]?.comment || ""
  // )
  const [comments, setComments] = useState('')
  const [level, setLevel] = useState("");
  //    const [selectedRecruiter, setSelectedRecruiter] = useState(item.peer_reviewer || "");
  const [selectedRecruiter, setSelectedRecruiter] = useState(
    item.peer_reviewer_level2 ? item.peer_reviewer_level2 : item.peer_reviewer_level1 || ""
  );
  const [showRecruiterDropdown, setShowRecruiterDropdown] = useState(false);
  const [priorityChecked, setPriorityChecked] = useState(false);
  useEffect(() => {
    setPriorityChecked(item.priority === "High");
  }, [item]);
  const { recruiters, managers } = useSelector((state) => state.userSliceReducer);
  const allUsers = [...(recruiters), ...(managers)];
  const filteredManagers = managers.filter(manager => manager.peer_reviewer_status === true);
  const filteredalluser = allUsers.filter(user => user.peer_reviewer_status === true);
  console.log(filteredManagers, "filteredManagers");
  const dispatch = useDispatch();
  const dashboardData = useSelector((state) => state.dashboardSliceReducer.dashboardData);

  const loggedInUsername = localStorage.getItem("username");

  const reviewerListToShow = filteredalluser.filter(user => user.username !== loggedInUsername);
  console.log(reviewerListToShow, "reviewerListToShow");
  // const reviewerListToShow =
  //   currentStatus === "Internal Screening Reject" ? filteredManagers : filteredalluser;
  // console.log(managers,"Recruiters")
  useEffect(() => {
    if (item && item.status) {
      const lowerCaseStatus = item.status;
      setCandidateStatus(lowerCaseStatus);
      //   const derivedStatus = "Peer Review" ? "Internal Screening" : lowerCaseStatus;

      // setCandidateStatus(derivedStatus); 
      setCurrentStatus(lowerCaseStatus);
      setLevel(statusToLevelMapping);
      setShowRecruiterDropdown(lowerCaseStatus === "Peer Review");
      console.log(
        "Initial candidate status and level set:",
        lowerCaseStatus,
        statusToLevelMapping,
      );
    }
  }, [item]);

  useEffect(() => {
    if (candidateStatus) {
      const lowerCaseStatus = candidateStatus.toLowerCase();
      setLevel(statusToLevelMapping);

      console.log(
        "Candidate status changed, new level set:",
        lowerCaseStatus,
        statusToLevelMapping,
      );
    }
  }, [candidateStatus]);

  // const { dashboardData } = useSelector((state) => state.dashboardSliceReducer);
  const handleFormSubmit = async (e) => {
    if (!waitForSubmission) {
      setwaitForSubmission(true);
      e.preventDefault();
      if (!statusChanged || candidateStatus === "Select Level") {
        toast.error("Please change the candidate status before submitting.");
        setwaitForSubmission(false);
        return;
      }
      try {
        const userId =
          localStorage.getItem("user_id") || location.state?.user_id;
        const recruiter_name =
          localStorage.getItem("user_name");
        if (!userId) {
          throw new Error("User ID is required");
        }
        const peer_reviewer = showRecruiterDropdown ? selectedRecruiter : null;
        const selectedRecruiterObj = reviewerListToShow.find(rec => rec.username === selectedRecruiter);
        const peer_reviewer_email = selectedRecruiterObj?.email || null;
        const priority = priorityChecked ? "High" : "Low";

        const response = await fetch(
          `http://192.168.0.47:5002/update_candidate/${item.id}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              candidate_status: candidateStatus === "Peer Review" ? "PR-Pending" : candidateStatus,

              // comments: comments,
              user_id: userId,
              user_name: recruiter_name,
              priority: priority,
              peer_reviewer: peer_reviewer,
              peer_reviewer_email: peer_reviewer_email,
              commentupdate: {
                [recruiter_name]: comments
              }
            }),
          },
        );

        const data = await response.json();
        if (!response.ok) {
          throw new Error("Failed to update candidate");
        }

        // Update Redux dashboard data directly


        const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
        store.dispatch((dispatch, getState) => {
          const state = getState();
          const existingDashboardData = state.dashboardSliceReducer.dashboardData;
          const existingCandidate = existingDashboardData.candidates.find(c => c.id === item.id);
          let updatedLevel1 = existingCandidate.peer_reviewer_level1;
          let updatedLevel2 = existingCandidate.peer_reviewer_level2;

          if (!updatedLevel2) {
            if (!updatedLevel1) {
              updatedLevel1 = peer_reviewer;
            } else {
              updatedLevel2 = peer_reviewer;
            }
          }

          //   update in the first 

          // const updatedCandidates = [
          //   {
          //     ...existingDashboardData.candidates.find(c => c.id === item.id),
          //     status: candidateStatus,
          //       peer_reviewer_level1: updatedLevel1,
          //       peer_reviewer_level2: updatedLevel2,
          //     priority: priorityChecked ? "High" : "Low",
          //     //  peer_reviewer: showRecruiterDropdown ? selectedRecruiter : null,
          //     comments: {
          //       ...existingDashboardData.candidates.find(c => c.id === item.id).comments,
          //       [recruiter_name]: [
          //         ...(existingDashboardData.candidates.find(c => c.id === item.id).comments?.[recruiter_name] || []),
          //         { comment: comments, timestamp:new Date().toLocaleString('sv-SE').replace("T", " ")}
          //       ]
          //     }
          //   },
          //   ...existingDashboardData.candidates.filter(c => c.id !== item.id)
          // ];

          // console.log("updatedCandidates",{
          //       ...existingDashboardData,
          //       candidates: updatedCandidates,
          //     })

          const updatedCandidates = existingDashboardData.candidates.map((candidate) => {
            if (candidate.id === item.id) {
              return {
                ...candidate,
                status: candidateStatus === "Peer Review" ? "PR-Pending" : candidateStatus,
                peer_reviewer_level1: updatedLevel1,
                peer_reviewer_level2: updatedLevel2,
                priority: priorityChecked ? "High" : "Low",
                comments: {
                  ...candidate.comments,
                  [recruiter_name]: [
                    ...(candidate.comments?.[recruiter_name] || []),
                    { comment: comments, timestamp: new Date().toLocaleString('sv-SE').replace("T", " ") }
                  ]
                }
              };
            }
            return candidate;
          });

          dispatch(
            setDashboardData({
              data: {
                ...existingDashboardData,
                candidates: updatedCandidates,
              }
            })
          );
          setwaitForSubmission(false);
          toast.success(data.message);
          navigate("/dashboard");
        });


        //     getDashboardData().then(() => {
        //           setwaitForSubmission(false);
        //           toast.success(data.message);
        //           navigate("/dashboard");
        //         });


      } catch (error) {
        console.error("Error updating candidate:", error);
        toast.error("Error updating candidate");
        setwaitForSubmission(false);
      }
    }
  };

  useEffect(() => {
    localStorage.setItem("path", location.state.path);
    console.log("Path set in local storage:", location.state.path);
  }, []);

  const handleLevelChange = (e) => {
    setLevel(e.target.innerText?.trim());
    setCandidateStatus(""); // Reset candidate status when level changes
    console.log("Level changed:", e.target.innerText?.trim());
  };

  const handleStatusChange = (e) => {
    const status = e.target.innerText?.trim();
    setCandidateStatus(status);
    // console.log("Status changed:", status);
    setStatusChanged(true);
    setOptionSelected(false);
    console.log("candidateStatus", candidateStatus);
    setShowRecruiterDropdown(status === "Peer Review");
    // setShowRecruiterDropdown(status.toLowerCase() === "Sent To Peer Review");
    if (status !== "Peer Review") {
      setSelectedRecruiter(""); // Clear previously selected reviewer
    }
  };
  const handleMouseEnter = () => {
    console.log("mouse enter");
    setOptionSelected(true);
  };

  return (
    <div className="wrapper">
      <LeftNav />
      <div className="section">
        <TitleBar />
        <div
          className="updatecandidatetable"
          style={{}}
        >
          <button
            className="back-button"
            onClick={() => navigate("/dashboard")}
          >
            <FaArrowLeft />
          </button>
          <h5
            className="title"
            style={{}}
          >
            Update Candidate Status
          </h5>
        </div>
        <div className="update-container">
          <form className="Form_UC" onSubmit={handleFormSubmit}>
            <table className="tbuc">
              <thead>
                <tr style={{ color: "black" }}>
                  <th className="th">Name</th>
                  <th className="th">Mobile</th>
                  <th className="th">Email</th>
                </tr>
              </thead>
              <tbody>
                <tr style={{ color: "black", textAlign: "center" }}>
                  <td className="tr">{item.name}</td>
                  <td className="tr">{item.mobile}</td>
                  <td className="tr">{item.email}</td>
                </tr>
              </tbody>
            </table>

            <h6
              style={{ marginTop: "10px", color: "darkblue", fontSize: "16px" }}
            >
              Candidate Current Status
            </h6>

            <div>
           
              <div className="menu" onMouseEnter={handleMouseEnter}>
                <ul>
                  <li>
                    <label
                      className="for-dropdown"
                      style={{ justifyContent: "center", bottom: "-5px" }}
                    >
                      Level: {candidateStatus}
                    </label>
                    {option_selected && (
                      <ul style={{ width:"300px",height:"300px",overflowY:"auto" ,overflowX:"hidden"}}>
                         {(
                          item.status === 'PR-Rejected' ||
                          item.status === 'SCREENING' ||
                          item.status === 'Internal Screening' ||
                          item.status === 'Internal Screening – In Progress'
                          // || item.status === 'Internal Screening Reject'
                        ) && (
                        <li className="link" onClick={handleStatusChange}>
                          Peer Review
                        </li>
                        )}
                        <li className="link" onClick={handleStatusChange}>

                          Internal Screening – In Progress
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Internal Screening – Rejected
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Internal Interview – In Progress
                        </li>
                
                        <li className="link" onClick={handleStatusChange}>

                          Internal Interview – Rejected
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Client Screening – In Progress
                        </li>

                        <li className="link" onClick={handleStatusChange}>
                          Client Screening – Rejected
                        </li>
                          <li className="link" onClick={handleStatusChange}>
                         Client – Duplicate Profile
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Client Interview – In Progress
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Client Interview – Rejected
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Client Interview – HR Round
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Client Interview – Managerial Round
                        </li>

                        <li className="link" onClick={handleStatusChange}>

                          Interview Reschedule - Client
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Interview Reschedule - Candidate
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Interview No-show
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Offer – Released
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Offer – Declined by Candidate
                        </li>
                        <li className="link" onClick={handleStatusChange}>
                          Offered – Yet to Join
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Onboarded
                        </li>  <li className="link" onClick={handleStatusChange}>

                          Position – On Hold
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Profile – On Hold
                        </li>
                        <li className="link" onClick={handleStatusChange}>

                          Candidate – Dropped
                        </li>
                   
                      </ul>
                    )}
                  </li>
                </ul>
              </div>

            </div>
     
            {showRecruiterDropdown && (
              <div className="input-fied">
                <label htmlFor="recruiter" style={{ color: "darkblue", margin: "5px 0px" }}>Select Peer Reviewer</label>
                <select
                  id="recruiter"
                  name="recruiter"
                  style={{ width: "250px" }}
                  value={selectedRecruiter}
                  onChange={(e) => setSelectedRecruiter(e.target.value)}
                  required
                >
                  <option disabled value="">
                    --Select Reviewer--
                  </option>
                  {reviewerListToShow.some((user) => user.user_type === "recruiter") && (
                    <optgroup label="----Select Recruiter----">
                      {reviewerListToShow
                        .filter((user) => user.user_type === "recruiter")
                        .map((rec) => (
                          <option key={rec.id} value={rec.username}>
                            {rec.username}
                          </option>
                        ))}
                    </optgroup>
                  )}

                  {reviewerListToShow.some((user) => user.user_type === "management") && (
                    <optgroup label="----Select Manager----">
                      {reviewerListToShow
                        .filter((user) => user.user_type === "management")
                        .map((rec) => (
                          <option key={rec.id} value={rec.username}>
                            {rec.username}
                          </option>
                        ))}
                    </optgroup>
                  )}
                </select>


                <label style={{ display: "flex", alignItems: "center", margin: "10px 0px", fontSize: "14px", gap: "4px" }}>
                  <span style={{ color: "red" }}> High Priority</span>
                  <input
                    type="checkbox"
                    style={{ cursor: "pointer" }}
                    checked={priorityChecked}
                    onChange={(e) => setPriorityChecked(e.target.checked)}
                  />

                </label>
              </div>
            )}


            <div className="input-fied">
              <label htmlFor="comments" style={{ color: "darkblue" }}>
                Comment
              </label>
              <br />
              <textarea
                name="comments"
                id="comments"
                className="Textarea"
                style={{
                  marginTop: "-25px",
                  height: "100px",
                  width: "250px",
                  marginBottom: "20px",
                }}
                onChange={(e) => setComments(e.target.value)}
                value={comments}
              ></textarea>
            </div>
            <div style={{ position: "relative" }}>
              <input
                type="submit"
                value={waitForSubmission ? "" : "Update Candidate"}
              />
              <ThreeDots
                wrapperClass="ovalSpinner"
                wrapperStyle={{
                  position: "absolute",
                  top: "-3px",
                  left: "60px",
                }}
                visible={waitForSubmission}
                height="45"
                width="45"
                color="white"
                ariaLabel="oval-loading"
              />
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default UpdateCandidate;
