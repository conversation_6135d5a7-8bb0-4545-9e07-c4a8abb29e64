.card-containers {
    display: flex;
}

.card_new {
    margin-top: 10px;
    background-color: #ffff;
    padding: 10px ;
    box-sizing: border-box;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1), /* Bottom shadow */
                0px -4px 6px rgba(0, 0, 0, 0.1), /* Top shadow */
                4px 0px 6px rgba(0, 0, 0, 0.1), /* Right shadow */
                -4px 0px 6px rgba(0, 0, 0, 0.1); /* Left shadow */
    /* display: flex; */
    /* flex-direction: column; */
}
.scatterplot svg.recharts-surface{
    margin-bottom: 2% !important;
    /* height: 100% !important; */
    margin-left: 0% !important;
    /* height: 350px; */
}
.expertise svg.recharts-surface{
    margin-bottom: 0% !important;
    /* height: 100% !important; */
    margin-left: 0% !important;
    /* height: 350px; */
}

.scatterplot .recharts-default-legend{
    visibility: hidden;
}
.timelearning svg.recharts-surface{
  margin-bottom: 0%;
   margin-left: 2%; 
  width: 90% !important;
}
.btnchart{
    display: flex;
    justify-content: center;
     margin-left: -9%;
}
.barbtn{
    padding: 6px 6px;
    margin: 5px;
    font-size: 15px;
    font-weight: 400;
    background-color: #fff;
    border: 1px solid #118dff;
    border-radius: 5px;
}
.barbtn.selected {
  /* Highlight color */
    color: #000;
    border: 2px solid #ff3c00;
    border-radius: 5px;
  }

canvas {
    width: 75% !important;
    height: 100% !important;
    margin-left: 5%;
}

.recharts-wrapper {
    /* width: 100% !important; */
    height: 100% !important;
}

/* #radarChart {

   box-sizing: border-box;
   display: block;
   height: 500px; 
   width: 1028px;
} */
/* #min-budget-gauge text {
   font-size: 1px !important;
} */
svg.recharts-surface {
    margin-bottom: 10%;
}

.custom-tooltip {
    background-color: #fff;
    /* White background color */
    border: 1px solid #ccc;
    /* Light gray border */
    border-radius: 4px;
    /* Rounded corners */
    padding: 10px;
    /* Padding inside the tooltip */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    /* Shadow effect */
    font-size: 14px;
    /* Font size for text */
    color: #333;
    /* Text color */
}



.custom-tooltip .intro {
    margin-top: 5px;
    /* Margin between label and introduction */
}

.custom-tooltip .desc {
    margin-top: 5px;
    /* Margin between introduction and description */
}

/* 
.text-group text {
   font-size: 20px !important;
   fill: rgb(0, 0, 0);
   text-anchor: middle;
} */

.bullet-points {
    color: black;
    text-align: left;
}

.bullet-points ul {
    list-style: none;
    /* Remove default list styling */
    padding-left: 0;
    /* Remove default padding */
    margin: 0;
    /* Remove default margin */
}

.bullet-points li {
    margin-bottom: 8px;
    /* Space between list items */
    position: relative;
    /* Allow positioning of bullet */
    padding-left: 20px;
    /* Space for the bullet */
}

.bullet-points li::before {
    content: '\2605';
    /* Bullet point character */
    position: absolute;
    left: 0;
    color: green;
    /* Bullet color */
    font-size: 20px;
    /* Bullet size */
}

.learningattitude tbody tr th {
    color: #ffffff;
    background: #32406d;
    font-size: 15px;
    padding: 5px 15px;
    text-align: center;
}

.learningattitude tbody tr td {
    border: 1px solid #ddd;
    height: 45px;
    padding: 10px 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 700px;
}

.learningattitude tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
}

.input_fieldanal {
    padding: 3px;
    border-radius: 5px;
    border: 1px solid #aaa;
    width: 150px;
}

.drop_downanal {
    padding-left: 4px;
    height: auto;
    max-height: 160px;
    width: 169px;
    overflow: auto;
    border-radius: 4px;
    background-color: white;
    position: absolute
}

.clientanal {
    padding: 4px;
    border-radius: 5px;
    border: 1px solid #aaa;
    width: 250px;
}

.resumeanal {
    padding: 3px;
    border-radius: 5px;
    border: 1px solid #aaa;
    width: 250px;
}
.buttonss{
   width: 100%;
    margin-left: 15px;
     margin-top: 30px;

}
.button_ss {
    border-radius: 4px;
    background: #32406D;
    color: #fff;
    border: none;
    cursor: pointer;
    min-width: 100px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.ss_formanal {
    font-family: Arial, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100px;
    width: 100%;
    margin-left: 0px;
}
.analy{
    display: flex;
    justify-content: center;
   
}
.candianaly {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    width: 100%;
    margin-left: 60px;
}

@media only screen and (max-width: 542px) {
    .card-containers {
        flex-direction: column;

    }

    .analy {
        overflow: auto;
    }

    #ss_formanal {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: auto;
        width: 100%;
        margin-left: 0px;
    }
    .clientanal{
       margin-bottom: 5px;
    }
    .candianaly {
       flex-direction: column;
       margin-left: 0px;
    }
    .btnchart{
        display: flex;
               justify-content: center;
    }
    .barbtn{
        width: 50px;
    }
    .card_new{
        width: 100% !important;
        height: 90% !important;
        overflow: auto;
    }
    .buttonss{
       margin-top: 10px;
       justify-items: center;
    }
    .button_ss{
       margin-left: 20%;
    }
    .input_fieldanal{
       margin-left: 20%;
       margin-bottom: 5px;
    }
    .analabel{
       margin-left: 20%;
    }
    body.active #ss_formanal,body.active .analy{
       display: block !important;

    }
    .ss_formanal{
        margin-bottom: -25px !important;
    }
    body.barside2 #ss_formanal,body.barside2 .analy{
       display:none;
    }
}

.ss_formanal {
    background: rgba(255, 255, 255, 0.25);
    /* box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
      backdrop-filter: blur(11.5px);
      -webkit-backdrop-filter: blur(11.5px);
      border-radius: 10px; */
    border: 1px solid rgba(255, 255, 255, 0.18);
    border-radius: 10px;
 
    border-radius: 5px;
    padding: 20px;
    /* width: 25rem; */
    width: 89%;
    height: 100px;
    margin-top: 20px;
    margin-left: 70px;
}
.gauge-chart-text {
    font-size: 12px; /* Adjust size as needed */
    fill: #000000; /* Ensure text color matches your chart */
  }
  .custom-gauge-chart text {
    font-size: 15px; /* Adjust the font size here */
  }
  

  #max-budget-guage text{
    font-size: 15px !important;
  }
  #expertise{
    max-height:600px;
    overflow:hidden;
 }
 #marketre{
   height:500px;
   overflow:auto;
 }
  @media screen and (max-width: 700px)  {
     #expertise{
        max-height:450px;
        overflow:hidden;
     }
     #marketre{
        height:450px;
        overflow:auto;
      }
  }

  .custom-gauge-chart text {
    font-size: 19px !important; /* Adjust the font size here */
  
  }
  @media screen and (max-width: 380px) {
  #expertise{
    height:370px;
    overflow: auto;
  }
  #marketre{
    height:370px;
    overflow: auto;
  }
 #learningatti {
    height:370px !important;
    overflow: auto;
  }
  #budgetest{
    height:370px !important;
    overflow: auto;
  }
  #careergrow{
    height:455px !important;
    overflow: auto;
  }
  }