/* ManagementLogin.css */

.background1 {
  /* Add styles for the background image */
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1; /* Lower z-index to keep it behind other content */
}

h1 {
  text-align: center;
  color: #0a0a0a;
  margin-bottom: 5px;
}

.ml_form {
  width: 400px;
  margin-left: 10rem;
  padding: 20px;
  margin-top: -6rem;
  border-radius: 15px;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  z-index: 1;
  position: relative;
  height: auto;
}

label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
  color: #080808;
}

#username_rl {
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 3px;
  box-sizing: border-box;
  height: auto;
}
#password_rl {
  width: 100%;
  padding: 10px;
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 3px;
  box-sizing: border-box;
  height: auto;
}

.login-button_rl {
  width: 100%;
  padding: 10px;
  background-color: #32406d;
  color: #fff;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

/* .login-button_rl:hover {
    background-color: #555;
} */

p {
  text-align: center;
  /* margin-top: 20px; */
  color: red;
}

.forgot {
  color: red;
  font-weight: 600;
}

.forgot:hover {
  color: darkred;
}

a {
  color: black;
  text-decoration: none;
}
.logo_1 {
  z-index: 2;
}
.logo_1 {
  position: absolute;
  top: 50px;
  left: 0px;
}
