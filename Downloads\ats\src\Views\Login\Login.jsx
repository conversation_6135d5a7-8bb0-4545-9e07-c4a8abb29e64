import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { RiEyeLine, RiEyeOffLine } from "react-icons/ri";
import { ThreeDots } from "react-loader-spinner";
import Modal from "react-modal";
import Cookies from "universal-cookie";
import CryptoJS from "crypto-js";
import { useDispatch } from "react-redux";
import { setDashboardData } from "../../store/slices/dashboardSlice";
import "./Login.css";

const cookies = new Cookies();

const Login = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const isSessionLogout = location.state?.isPopupvisible;

  // State management
  const [activeRole, setActiveRole] = useState("manager");
  const [waitForSubmission, setWaitForSubmission] = useState(false);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({
    username: "",
    password: "",
  });
  const [loginError, setLoginError] = useState({
    username_error: "",
    password_error: "",
    general_error: "",
  });
  const [validationState, setValidationState] = useState({
    username: null,
    password: null,
  });

  // Clear localStorage and redux state on component mount
  useEffect(() => {
    localStorage.removeItem("user_id");
    localStorage.removeItem("user_type");
    localStorage.removeItem("user_name");
    localStorage.removeItem("profileImage");
    dispatch(setDashboardData({ data: {} }));
  }, [dispatch]);

  // Handle session timeout popup
  useEffect(() => {
    if (isSessionLogout) {
      setIsPopupVisible(true);
    }
  }, [isSessionLogout]);

  // Email validation
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Password validation
  const validatePassword = (password) => {
    return password.length >= 6;
  };

  // Handle input changes with real-time validation
  const changeCredentials = (e) => {
    const { name, value } = e.target;
    setCredentials({ ...credentials, [name]: value });

    // Clear error messages when user starts typing
    if (name === "username") {
      setLoginError((prevError) => ({
        ...prevError,
        username_error: "",
        general_error: "",
      }));

      // Real-time email validation
      if (value) {
        const isValid = validateEmail(value);
        setValidationState(prev => ({ ...prev, username: isValid }));
        if (!isValid && value.length > 3) {
          setLoginError((prevError) => ({
            ...prevError,
            username_error: "Please enter a valid email address",
          }));
        }
      } else {
        setValidationState(prev => ({ ...prev, username: null }));
      }
    } else if (name === "password") {
      setLoginError((prevError) => ({
        ...prevError,
        password_error: "",
        general_error: "",
      }));

      // Real-time password validation
      if (value) {
        const isValid = validatePassword(value);
        setValidationState(prev => ({ ...prev, password: isValid }));
        if (!isValid && value.length > 0) {
          setLoginError((prevError) => ({
            ...prevError,
            password_error: "Password must be at least 6 characters",
          }));
        }
      } else {
        setValidationState(prev => ({ ...prev, password: null }));
      }
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  // Handle role switching
  const handleRoleSwitch = (role) => {
    setActiveRole(role);
    // Clear any existing errors when switching roles
    setLoginError({
      username_error: "",
      password_error: "",
      general_error: "",
    });
    setValidationState({
      username: null,
      password: null,
    });
  };

  // Handle forgot password
  const handleForgotPassword = (e) => {
    e.preventDefault();
    navigate("/ForgotPassword");
  };

  // Handle form submission
  const handleCredentials = async (e) => {
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      e.preventDefault();

      // Reset errors
      setLoginError({
        username_error: "",
        password_error: "",
        general_error: "",
      });

      // Validation
      let hasErrors = false;
      const newErrors = {};

      if (credentials.username.length === 0) {
        newErrors.username_error = "Email field cannot be empty";
        hasErrors = true;
      } else if (!validateEmail(credentials.username)) {
        newErrors.username_error = "Please enter a valid email address";
        hasErrors = true;
      }

      if (credentials.password.length === 0) {
        newErrors.password_error = "Password field cannot be empty";
        hasErrors = true;
      } else if (!validatePassword(credentials.password)) {
        newErrors.password_error = "Password must be at least 6 characters";
        hasErrors = true;
      }

      if (hasErrors) {
        setLoginError(newErrors);
        setWaitForSubmission(false);
        return;
      }

      // Encrypt password
      const secretKey = "ATS@mako";
      const encryptedPassword = CryptoJS.AES.encrypt(
        credentials.password,
        secretKey
      ).toString();

      try {
        // Determine API endpoint based on active role
        const endpoint = activeRole === "manager"
          ? "http://************:5002/login/management"
          : "http://************:5002/login/recruiter";

        const requestBody = activeRole === "manager"
          ? {
            username: credentials.username,
            password: encryptedPassword,
          }
          : {
            username: credentials.username,
            password: encryptedPassword,
            user_type: "recruiter",
            user_id: localStorage.getItem("user_id"),
          };

        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();

        if (response.ok) {
          cookies.set("USERNAME", credentials.username, { path: "/" });
          cookies.set("USERTYPE", activeRole === "manager" ? "management" : "recruiter", { path: "/" });

          if (data.status === "error") {
            setWaitForSubmission(false);
            setLoginError({
              username_error: "",
              password_error: "",
              general_error: data.message,
            });
            toast.error(data.message);
          } else {
            const userName = data.name || "Default Name";
            const userEmail = data.email || "Default Name";

            localStorage.setItem("user_id", data.user_id);
            localStorage.setItem("username", credentials.username);
            localStorage.setItem("name", userName);
            localStorage.setItem("email", userEmail);

            toast.success("Login successful!");

            navigate(data.redirect, {
              state: {
                user_type: activeRole === "manager" ? "management" : "recruitment",
                user_id: data.user_id,
                user_name: credentials.username,
                name: userName,
                email: userEmail,
              },
            });
          }
        } else {
          setWaitForSubmission(false);
          setLoginError({
            username_error: "",
            password_error: "",
            general_error: "Invalid credentials. Please check your email and password.",
          });
          toast.error("Login failed. Please check your credentials.");
        }
      } catch (err) {
        setWaitForSubmission(false);
        setLoginError({
          username_error: "",
          password_error: "",
          general_error: "Network error. Please check your connection and try again.",
        });
        toast.error("An error occurred while processing your request.");
      }
    }
  };

  return (
    <div className="login-container">
      {/* Floating Background Elements */}
      <div className="floating-circle-1"></div>
      <div className="floating-circle-2"></div>
      <div className="floating-circle-3"></div>

      <div className="login-content">
        <div className="login-form-container">
          <div className="welcome-section">
            <h1 className="welcome-title">Welcome Back</h1>
            <p className="welcome-subtitle">Sign in to your HR management portal</p>
          </div>

          <div className="role-switcher">
            <button
              type="button"
              className={`role-button ${activeRole === "manager" ? "active" : ""}`}
              onClick={() => handleRoleSwitch("manager")}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="currentColor" />
                <path d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z" fill="currentColor" />
              </svg>
              Manager
            </button>
            <button
              type="button"
              className={`role-button ${activeRole === "recruiter" ? "active" : ""}`}
              onClick={() => handleRoleSwitch("recruiter")}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" fill="currentColor" />
                <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z" fill="currentColor" />
              </svg>
              Recruiter
            </button>
          </div>

          {/* General Error Message */}
          {loginError.general_error && (
            <div className="general-error-message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor" />
              </svg>
              {loginError.general_error}
            </div>
          )}

          <form onSubmit={handleCredentials} className="login-form">
            <div className="form-group">
              <label htmlFor="username" className="form-label">Username</label>
              <div className="input-container">
                <input
                  id="username"
                  name="username"
                  type="email"
                  autoComplete="email"
                  className={`form-input ${loginError.username_error
                    ? "error"
                    : validationState.username === true
                      ? "success"
                      : ""
                    }`}
                  placeholder="Enter your username"
                  value={credentials.username}
                  onChange={changeCredentials}
                />
                {validationState.username === true && (
                  <div className="input-success-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor" />
                    </svg>
                  </div>
                )}
              </div>
              {loginError.username_error && (
                <div className="error-message">
                  {loginError.username_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="password" className="form-label">Password</label>
              <div className="password-input-container">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  className={`form-input ${loginError.password_error
                    ? "error"
                    : validationState.password === true
                      ? "success"
                      : ""
                    }`}
                  placeholder="Enter your password"
                  value={credentials.password}
                  onChange={changeCredentials}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={togglePasswordVisibility}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <RiEyeLine /> : <RiEyeOffLine />}
                </button>
              </div>
              {loginError.password_error && (
                <div className="error-message">
                  {loginError.password_error}
                </div>
              )}
            </div>

            <button
              type="submit"
              className="submit-button"
              disabled={waitForSubmission}
            >
              {waitForSubmission ? (
                <>
                  <ThreeDots
                    height="20"
                    width="20"
                    color="white"
                    ariaLabel="loading"
                  />
                  <span>Signing in...</span>
                </>
              ) : (
                `Sign in as ${activeRole === "manager" ? "Manager" : "Recruiter"}`
              )}
            </button>
            <div className="footer-links">
              <button
                type="button"
                className="forgot-password"
                onClick={handleForgotPassword}
              >
                Forgot your password?
              </button>
              <button
                type="button"
                className="register-button"
                onClick={() => navigate("/Register")}
              >
                Register
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Session Timeout Modal */}
      <Modal
        isOpen={isPopupVisible}
        onRequestClose={() => setIsPopupVisible(false)}
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            backdropFilter: "blur(4px)",
            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            position: "relative",
            top: "auto",
            left: "auto",
            right: "auto",
            bottom: "auto",
            width: "400px",
            maxWidth: "90vw",
            height: "auto",
            margin: "0",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "16px",
            boxShadow: "0 20px 40px rgba(0, 0, 0, 0.15)",
            padding: "32px 24px",
            border: "none",
          },
        }}
        ariaHideApp={false}
      >
        <div className="modal-content">
          <div className="modal-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="#F59E0B" />
            </svg>
          </div>
          <div className="modal-header">
            <h2>Session Expired</h2>
          </div>
          <div className="modal-body">
            <p>Your session has timed out for security reasons. Please log in again to continue.</p>
          </div>
          <div className="modal-footer">
            <button
              className="modal-ok"
              onClick={() => {
                setIsPopupVisible(false);
                navigate("/Login");
              }}
            >
              Continue
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Login;