import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { RiEyeLine, RiEyeOffLine } from "react-icons/ri";
import { ThreeDots } from "react-loader-spinner";
import Modal from "react-modal";
import Cookies from "universal-cookie";
import CryptoJS from "crypto-js";
import { useDispatch } from "react-redux";
import { setDashboardData } from "../../store/slices/dashboardSlice";
import "./Login.css";
import Logo from "../../assets/Logo.png";

const cookies = new Cookies();

const Login = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const isSessionLogout = location.state?.isPopupvisible;

  // State management
  const [activeRole, setActiveRole] = useState("manager"); // "manager" or "recruiter"
  const [waitForSubmission, setWaitForSubmission] = useState(false);
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [credentials, setCredentials] = useState({
    username: "",
    password: "",
  });
  const [loginError, setLoginError] = useState({
    username_error: "",
    password_error: "",
  });

  // Clear localStorage and redux state on component mount
  useEffect(() => {
    localStorage.removeItem("user_id");
    localStorage.removeItem("user_type");
    localStorage.removeItem("user_name");
    localStorage.removeItem("profileImage");
    dispatch(setDashboardData({ data: {} }));
  }, [dispatch]);

  // Handle session timeout popup
  useEffect(() => {
    if (isSessionLogout) {
      setIsPopupVisible(true);
    }
  }, [isSessionLogout]);

  // Handle input changes
  const changeCredentials = (e) => {
    const { name, value } = e.target;
    setCredentials({ ...credentials, [name]: value });

    // Clear error messages when user starts typing
    if (name === "username") {
      setLoginError((prevError) => ({
        ...prevError,
        username_error: "",
      }));
    } else if (name === "password") {
      setLoginError((prevError) => ({
        ...prevError,
        password_error: "",
      }));
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  // Handle role switching
  const handleRoleSwitch = (role) => {
    setActiveRole(role);
    // Clear any existing errors when switching roles
    setLoginError({
      username_error: "",
      password_error: "",
    });
  };

  // Handle forgot password
  const handleForgotPassword = (e) => {
    e.preventDefault();
    navigate("/ForgotPassword");
  };

  // Handle form submission
  const handleCredentials = async (e) => {
    if (!waitForSubmission) {
      setWaitForSubmission(true);
      e.preventDefault();

      // Validation
      if (credentials.username.length === 0) {
        setWaitForSubmission(false);
        setLoginError((loginError) => ({
          ...loginError,
          username_error: "Username field cannot be empty",
        }));
        return;
      } else {
        setLoginError((loginError) => ({ ...loginError, username_error: "" }));
      }

      if (credentials.password.length === 0) {
        setWaitForSubmission(false);
        setLoginError((loginError) => ({
          ...loginError,
          password_error: "Password field cannot be empty",
        }));
        return;
      } else {
        setLoginError((loginError) => ({ ...loginError, password_error: "" }));
      }

      // Encrypt password
      const secretKey = "ATS@mako";
      const encryptedPassword = CryptoJS.AES.encrypt(
        credentials.password,
        secretKey
      ).toString();

      try {
        // Determine API endpoint based on active role
        const endpoint = activeRole === "manager"
          ? "  http://192.168.0.47:5002/login/management"
          : "  http://192.168.0.47:5002/login/recruiter";

        const requestBody = activeRole === "manager"
          ? {
              username: credentials.username,
              password: encryptedPassword,
            }
          : {
              username: credentials.username,
              password: encryptedPassword,
              user_type: "recruiter",
              user_id: localStorage.getItem("user_id"),
            };

        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        });

        const data = await response.json();
        if (response.ok) {
          cookies.set("USERNAME", credentials.username, { path: "/" });
          cookies.set("USERTYPE", activeRole === "manager" ? "managment" : "recruiter", { path: "/" });

          if (data.status === "error") {
            setWaitForSubmission(false);
            toast.error(data.message);
          } else {
            const userName = data.name || "Default Name";
            const userEmail = data.email || "Default Name";

            localStorage.setItem("user_id", data.user_id);
            localStorage.setItem("username", credentials.username);
            localStorage.setItem("name", userName);
            localStorage.setItem("email", userEmail);

            navigate(data.redirect, {
              state: {
                user_type: activeRole === "manager" ? "management" : "recruitment",
                user_id: data.user_id,
                user_name: credentials.username,
                name: userName,
                email: userEmail,
              },
            });
          }
        }
      } catch (err) {
        setWaitForSubmission(false);
        toast.error("An error occurred while processing your request.");
      }
    }
  };

  return (
    <div className="login-container">
      <div className="login-background"></div>

      <div className="login-content">
        <div className="logo-container">
          <img className="logo" src={Logo} alt="logo" />
        </div>

        <div className="login-form-container">
          <div className="welcome-section">
            <div className="welcome-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#4F46E5"/>
                <path d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z" fill="#4F46E5"/>
              </svg>
            </div>
            <h1 className="welcome-title">Welcome Back</h1>
            <p className="welcome-subtitle">Access your HR management dashboard</p>
          </div>

          <div className="role-switcher">
            <button
              type="button"
              className={`role-button ${activeRole === "manager" ? "active" : ""}`}
              onClick={() => handleRoleSwitch("manager")}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="currentColor"/>
                <path d="M12 14C7.58172 14 4 17.5817 4 22H20C20 17.5817 16.4183 14 12 14Z" fill="currentColor"/>
              </svg>
              HR Manager
            </button>
            <button
              type="button"
              className={`role-button ${activeRole === "recruiter" ? "active" : ""}`}
              onClick={() => handleRoleSwitch("recruiter")}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" fill="currentColor"/>
                <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z" fill="currentColor"/>
              </svg>
              Recruiter
            </button>
          </div>

          <form onSubmit={handleCredentials} className="login-form">
            <div className="form-group">
              <label htmlFor="username" className="form-label">Email</label>
              <input
                id="username"
                name="username"
                type="text"
                className="form-input"
                placeholder={`Enter your ${activeRole} email`}
                value={credentials.username}
                onChange={changeCredentials}
              />
              {loginError.username_error && (
                <div className="error-message">
                  {loginError.username_error}
                </div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="password" className="form-label">Password</label>
              <div className="password-input-container">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  className="form-input"
                  placeholder="Enter your secure password"
                  value={credentials.password}
                  onChange={changeCredentials}
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? <RiEyeLine /> : <RiEyeOffLine />}
                </button>
              </div>
              {loginError.password_error && (
                <div className="error-message">
                  {loginError.password_error}
                </div>
              )}
            </div>

            <button
              type="submit"
              className="submit-button"
              disabled={waitForSubmission}
            >
              {waitForSubmission ? (
                <ThreeDots
                  height="20"
                  width="20"
                  color="white"
                  ariaLabel="loading"
                />
              ) : (
                `Sign in as ${activeRole === "manager" ? "HR Manager" : "Recruiter"}`
              )}
            </button>

            <div className="form-footer">
              <button
                type="button"
                className="forgot-password-link"
                onClick={handleForgotPassword}
              >
                Forgot your password?
              </button>

              <div className="signup-link">
                Don't have an account? <span className="signup-text">Sign up</span>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Session Timeout Modal */}
      <Modal
        isOpen={isPopupVisible}
        style={{
          overlay: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            backdropFilter: "blur(0.5px)",
            zIndex: 9999,
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          },
          content: {
            width: "350px",
            height: "160px",
            margin: "auto",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            background: "white",
            borderRadius: "10px",
            boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.5)",
            padding: "20px 20px 10px",
          },
        }}
      >
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>Session Timeout</h2>
            </div>
            <div className="modal-body">
              <p>Your session has timed out. You have been logged out</p>
            </div>
            <div className="modal-footer">
              <button
                className="modal-ok"
                onClick={() => {
                  setIsPopupVisible(false);
                  navigate("/Login");
                }}
              >
                Ok
              </button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Login;
