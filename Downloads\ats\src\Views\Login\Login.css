/* Modern Professional Login Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 20px;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80') center/cover;
  opacity: 0.08;
  z-index: 1;
}

.login-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

.logo-container {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  height: 60px;
  width: auto;
  filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.login-form-container {
  background: rgba(255, 255, 255, 0.96);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px 40px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-form-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.welcome-section {
  text-align: center;
  margin-bottom: 32px;
}

.welcome-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
  border-radius: 20px;
  margin-bottom: 24px;
  box-shadow: 0 10px 30px rgba(79, 70, 229, 0.4);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.welcome-icon:hover {
  transform: scale(1.05);
  box-shadow: 0 15px 35px rgba(79, 70, 229, 0.5);
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: #1F2937;
  margin-bottom: 8px;
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, #1F2937 0%, #4F46E5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 16px;
  color: #6B7280;
  font-weight: 400;
  line-height: 1.5;
}

/* Role Switcher */
.role-switcher {
  display: flex;
  background: #F3F4F6;
  border-radius: 16px;
  padding: 6px;
  margin-bottom: 32px;
  gap: 6px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.role-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 16px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #6B7280;
  position: relative;
  overflow: hidden;
}

.role-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.role-button:hover::before {
  left: 100%;
}

.role-button.active {
  background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
  transform: translateY(-1px);
}

.role-button:hover:not(.active) {
  background: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
  transform: translateY(-1px);
}

/* General Error Message */
.general-error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #FEF2F2 0%, #FEE2E2 100%);
  border: 1px solid #FECACA;
  border-radius: 12px;
  color: #DC2626;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 24px;
  animation: slideDown 0.3s ease;
}

.general-error-message svg {
  color: #DC2626;
  flex-shrink: 0;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 16px;
  border: 2px solid #E5E7EB;
  border-radius: 12px;
  font-size: 16px;
  background: #FFFFFF;
  transition: all 0.3s ease;
  outline: none;
  font-family: inherit;
}

.form-input:focus {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  transform: translateY(-1px);
}

.form-input.success {
  border-color: #10B981;
  background: #F0FDF4;
}

.form-input.error {
  border-color: #EF4444;
  background: #FEF2F2;
  animation: shake 0.3s ease;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.form-input::placeholder {
  color: #9CA3AF;
  transition: opacity 0.3s ease;
}

.form-input:focus::placeholder {
  opacity: 0.7;
}

.input-success-icon {
  position: absolute;
  right: 16px;
  color: #10B981;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: checkmark 0.3s ease;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
  font-size: 20px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.password-toggle:hover {
  color: #4F46E5;
  background: rgba(79, 70, 229, 0.1);
  transform: scale(1.1);
}

.error-message {
  color: #EF4444;
  font-size: 14px;
  font-weight: 500;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 6px;
  animation: slideDown 0.3s ease;
}

.error-message::before {
  content: '⚠';
  font-size: 12px;
}

/* Submit Button */
.submit-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 56px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.submit-button:disabled::before {
  left: 0;
}

.submit-button:disabled span {
  color: #A1A1AA;
}
