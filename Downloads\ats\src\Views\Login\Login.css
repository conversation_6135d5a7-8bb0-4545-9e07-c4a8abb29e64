/* Animated Gradient Login Design */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Main Login Container with Office Background */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)),
              url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  padding: 1rem;
  position: relative;
  overflow: hidden;
}

/* Subtle Overlay Elements for Office Background */
.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  z-index: 1;
  animation: subtleFloat 15s ease-in-out infinite;
}

.login-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  z-index: 1;
  animation: shimmer 20s ease-in-out infinite;
}

/* Subtle Animation Keyframes for Office Background */
@keyframes subtleFloat {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0px);
  }
  50% {
    opacity: 0.5;
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
}

.login-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 28rem;
}

.login-form-container {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Subtle Floating Elements for Office Background */
.login-container .floating-circle-1 {
  position: absolute;
  top: 10%;
  left: 15%;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: floatSlow 15s ease-in-out infinite;
  z-index: 2;
}

.login-container .floating-circle-2 {
  position: absolute;
  bottom: 15%;
  right: 20%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(118, 75, 162, 0.08) 0%, transparent 70%);
  border-radius: 50%;
  animation: floatReverse 12s ease-in-out infinite;
  z-index: 2;
}

.login-container .floating-circle-3 {
  position: absolute;
  top: 60%;
  left: 5%;
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.06) 0%, transparent 70%);
  border-radius: 50%;
  animation: floatSlow 18s ease-in-out infinite reverse;
  z-index: 2;
}

@keyframes floatSlow {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-15px) translateX(10px);
  }
  50% {
    transform: translateY(-30px) translateX(0px);
  }
  75% {
    transform: translateY(-15px) translateX(-10px);
  }
}

@keyframes floatReverse {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  50% {
    transform: translateY(25px) translateX(-15px) rotate(180deg);
  }
}

/* Welcome Header Section */
.welcome-section {
  text-align: center;
  margin-bottom: 2rem;
  margin-top: -1rem;
}

.welcome-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  animation: iconFloat 3s ease-in-out infinite;
}

.welcome-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

@keyframes iconFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.welcome-title {
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.5rem;
}

.welcome-subtitle {
  color: #6B7280;
  font-size: 1rem;
  margin-bottom: 0;
}

/* Role Switcher */
.role-switcher {
  background: rgba(102, 126, 234, 0.1);
  backdrop-filter: blur(10px);
  padding: 4px;
  border-radius: 12px;
  display: flex;
  gap: 4px;
  margin-bottom: 2rem;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.role-button {
  flex: 1;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #6B7280;
}

.role-button.active {
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transform: translateY(-1px);
}

.role-button:hover:not(.active) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* Form Styling */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.input-container, .password-input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  font-size: 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: #374151;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.form-input::placeholder {
  color: #9CA3AF;
  font-weight: 400;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Password Toggle Button */
.password-toggle {
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #9CA3AF;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
  padding: 4px;
}

.password-toggle:hover {
  color: #667eea;
}

/* Submit Button */
.submit-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  padding: 16px;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-button:disabled::before {
  display: none;
}


/* Footer Links */
.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  font-size: 14px;
}

.forgot-password {
  font-weight: 500;
  color: #667eea;
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
}

.forgot-password:hover {
  color: #764ba2;
}

.session-expired {
  color: #9CA3AF;
  font-size: 14px;
}

/* Error and Success States */
.error-message {
  color: #EF4444;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .login-container {
    padding: 16px;
  }

  .login-form-container {
    padding: 2rem 1.5rem;
    border-radius: 1.25rem;
  }

  .welcome-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
  }

  .welcome-title {
    font-size: 2rem;
  }

  .role-switcher {
    flex-direction: column;
    gap: 8px;
  }

  .role-button {
    width: 100%;
    justify-content: center;
  }

  .floating-circle-1,
  .floating-circle-2,
  .floating-circle-3 {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .login-form-container {
    padding: 1.5rem 1.25rem;
    border-radius: 1rem;
  }

  .welcome-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin-bottom: 1rem;
  }

  .welcome-title {
    font-size: 1.75rem;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .form-input {
    padding: 12px 14px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .submit-button {
    padding: 14px 16px;
    font-size: 16px;
  }

  .footer-links {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

.form-input.error {
  border-color: #ef4444; /* red-500 */
}

.form-input.error:focus {
  box-shadow: 0 0 0 2px #ef4444;
  border-color: #ef4444;
}

.form-input.success {
  border-color: #22c55e; /* green-500 */
}

.form-input.success:focus {
  box-shadow: 0 0 0 2px #22c55e;
  border-color: #22c55e;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .login-content {
      padding: 2rem;
  }
}
