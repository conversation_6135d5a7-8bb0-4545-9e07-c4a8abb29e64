/* Modern Unified Login Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80') center/cover;
  opacity: 0.1;
  z-index: 1;
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
}

.logo-container {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  height: 60px;
  width: auto;
  filter: brightness(0) invert(1);
}

.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-section {
  text-align: center;
  margin-bottom: 32px;
}

.welcome-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
  border-radius: 20px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(79, 70, 229, 0.3);
}

.welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: #1F2937;
  margin-bottom: 8px;
  letter-spacing: -0.025em;
}

.welcome-subtitle {
  font-size: 16px;
  color: #6B7280;
  font-weight: 400;
  line-height: 1.5;
}

/* Role Switcher */
.role-switcher {
  display: flex;
  background: #F3F4F6;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 32px;
  gap: 4px;
}

.role-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  color: #6B7280;
}

.role-button.active {
  background: #4F46E5;
  color: white;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.role-button:hover:not(.active) {
  background: rgba(79, 70, 229, 0.1);
  color: #4F46E5;
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.form-input {
  width: 100%;
  padding: 16px;
  border: 2px solid #E5E7EB;
  border-radius: 12px;
  font-size: 16px;
  background: #FFFFFF;
  transition: all 0.2s ease;
  outline: none;
}

.form-input:focus {
  border-color: #4F46E5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input::placeholder {
  color: #9CA3AF;
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  color: #6B7280;
  font-size: 20px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.password-toggle:hover {
  color: #4F46E5;
}

.error-message {
  color: #EF4444;
  font-size: 14px;
  font-weight: 500;
  margin-top: 4px;
}

/* Submit Button */
.submit-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 56px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.submit-button:active:not(:disabled) {
  transform: translateY(0);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Form Footer */
.form-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.forgot-password-link {
  background: none;
  border: none;
  color: #4F46E5;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.forgot-password-link:hover {
  color: #7C3AED;
  text-decoration: underline;
}

.signup-link {
  font-size: 14px;
  color: #6B7280;
}

.signup-text {
  color: #4F46E5;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s ease;
}

.signup-text:hover {
  color: #7C3AED;
}

/* Modal Styles */
.modal-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.modal {
  text-align: center;
}

.modal-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 12px;
}

.modal-body p {
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

.modal-ok {
  background: #4F46E5;
  color: white;
  border: none;
  padding: 8px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.modal-ok:hover {
  background: #7C3AED;
}

/* Responsive Design */
@media (max-width: 640px) {
  .login-content {
    padding: 16px;
  }

  .login-form-container {
    padding: 32px 24px;
    border-radius: 20px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
  }

  .role-button {
    padding: 10px 12px;
    font-size: 13px;
  }

  .form-input {
    padding: 14px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .submit-button {
    padding: 14px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .login-form-container {
    padding: 24px 20px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }
}