/* General Body and Font Styles */
body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  background-image: linear-gradient(to top right, #ede9fe, #dbeafe);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
  box-sizing: border-box;
}

/* Main Login Container */
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.login-content {
  background-color: white;
  padding: 3.5rem;
  border-radius: 0.75rem; /* 12px */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 48rem;
}

/* Welcome Header Section */
.welcome-section {
  text-align: center;
  margin-bottom: 1rem;
  margin-top: -2.5rem;
}

.welcome-title {
  font-size: 1.875rem; /* 30px */
  font-weight: 700;
  background-image: linear-gradient(to right, #7c3aed, #2563eb);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.welcome-subtitle {
  color: #6b7280; /* gray-500 */
  margin-top: 0.5rem;
}

/* Role Switcher */
.role-switcher {
  background-color: #f3f4f6; /* gray-100 */
  padding: 0.25rem;
  border-radius: 0.5rem; /* 8px */
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.role-button {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem; /* 6px */
  font-size: 0.875rem; /* 14px */
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
  color: #4b5563; /* gray-600 */
}

.role-button.active {
  color: white;
  background-color: #2563eb; /* blue-600 */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Form Styling */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem; /* 14px */
  font-weight: 500;
  color: #374151; /* gray-700 */
  margin-bottom: 0.25rem;
}

.input-container, .password-input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.625rem 1rem; /* 10px 16px */
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.5rem; /* 8px */
  transition: all 0.2s ease;
  box-sizing: border-box; /* Important for width calculation */
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6; /* ring-2 ring-blue-500 */
  border-color: #3b82f6;
}

/* Password Toggle Button */
.password-toggle {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  padding: 0 0.75rem;
  display: flex;
  align-items: center;
  color: #9ca3af; /* gray-400 */
  background: none;
  border: none;
  cursor: pointer;
  height: 100%;
}

.password-toggle:hover {
  color: #4b5563; /* gray-600 */
}

/* Submit Button */
.submit-button {
  width: 100%;
  background-image: linear-gradient(to right, #7c3aed, #2563eb);
  color: white;
  font-weight: 600;
  padding: 0.75rem 0;
  border-radius: 0.5rem; /* 8px */
  border: none;
  cursor: pointer;
  transition: opacity 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.submit-button:hover {
  opacity: 0.9;
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}


/* Footer Links */
.footer-links {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  font-size: 0.875rem; /* 14px */
}

.forgot-password, .register-button {
  font-weight: 500;
  color: #2563eb; /* blue-600 */
  text-decoration: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.forgot-password:hover, .register-button:hover {
  color: #1d4ed8; /* blue-500 */
}

/* Error and Success States (Optional but good for UX) */
.error-message {
  color: #ef4444; /* red-500 */
  font-size: 0.75rem; /* 12px */
  margin-top: 0.25rem;
}

.form-input.error {
  border-color: #ef4444; /* red-500 */
}

.form-input.error:focus {
  box-shadow: 0 0 0 2px #ef4444;
  border-color: #ef4444;
}

.form-input.success {
  border-color: #22c55e; /* green-500 */
}

.form-input.success:focus {
  box-shadow: 0 0 0 2px #22c55e;
  border-color: #22c55e;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .login-content {
      padding: 2rem;
  }
}
