.container_rc {
  position: relative;
  height: 74vh;
  overflow: hidden;
  flex:1;
  width: 100%;
  /* margin-top: -36px; */
  padding: 15px 0 5px;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  /* overflow: auto; */
}

p {
  font-size: 15px;
}
.card:hover {
  box-shadow: 5px 5px 10px #9c39ff;
  transition: box-shadow 0.1s ease-in;
}

/* Style for card container */
.card {
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f0f0f0;
  padding: 10px;
  position: relative;
  height: 80px;
  width: 200px;
  word-break: break-all;
}

/* Style for card header */
.card-header {
  background-color: #f0f0f0;
  padding: 8px;
  padding-top: 5px;
  font-weight: 600;
  width: auto;
  font-size: 17px;
  margin-top: -10px;
  display: flex;
  justify-content: left; /* Center horizontally */
  align-items: left; /* Center vertically */
  cursor: pointer;
  height: 45px; /* Fixed height */
  /* font-family: 'Times New Roman', Times, serif; */
  font-family: "roboto";
}

/* Style for card body */
.card-body {
  padding: 6px 6px;
  background-color: #f0f0f0;
  /* margin-top:10px; */
  align-items: center;
  position: absolute;
  bottom: 0;
}

/* Style for Add Candidate button */
.btn_dark {
  background-color: #32406d;
  color: #fff;
  border-radius: 3px;
  padding: 7px 7px; /* Increased padding for larger button */
  font-size: 14px; /* Increased font size for larger button */
  max-width: 160px;
  font-weight: 500;
  border: none;
  outline: none;
  transition: all 0.7s linear;
  cursor: pointer;
  /* margin-left: 19px; */
  margin-top: -25px;
  text-decoration: none;
  font-size: 13px;
}

/* .btn_dark:hover {
  background-color:  #ffffff;
  color: #343a40;;
  border: none;
} */

/* Style for heading */
.heading2 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

/* Style for container */
/* .container {
  margin-top: 20px;
}
 */
/* Style for row */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

/* Style for column */
.col-md-3 {
  flex: 0 0 25%;
  /* max-width: 25%; */
  padding-right: 15px;
  padding-left: 15px;
}

@media (max-width: 992px) {
  .col-md-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  .col-md-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media screen and (max-height: 680px) and (min-width: 1000px) {
  /* .container_rc {
    height: 68vh !important;
  } */
  /* .card_container {
    height: 64vh !important;
  } */

  .section {
    margin-left: 190px !important;
  }
/* 
  .pagnBar {
    margin-top: -38px !important;
  } */

  body.active .wrapper .section {
    margin-left: 0 !important;
    width: 100%;
  }
}

@media screen and (max-width: 767px) {
  /* .showing{
    text-align:center !important;
    margin-left: 80px !important;
  } */
  /* .pagination{
    justify-content: center !important;
  } */
  .card_container{
    display: grid;
    grid-template-columns: 1fr 1fr !important;
    gap: 10px;
    overflow: auto;
    justify-items: center;
}
.card{
  width: 179px !important;
}
  }

  @media only screen and (max-width: 542px) {
     /* body.active .theader, */
      body.active .container_rc{
   
       display:block !important;
   
    }
    .theader .heading2{
    margin-top: 0px !important;
    padding:2px;
    } 
   
    body.barsi .theader,body.active .container_rc {
      display: none ;
    }
    body.active .wrapper .sidebar {
      left: -300px !important;
    }
   
  }
  @media screen and (min-width: 320px)  and (max-width:375px){
  .Searched{
    width: 240px !important;
  }
  .card_container{
    display: grid;
    grid-template-columns: 1fr 1fr !important;
    gap: 10px;
    overflow: auto;
    justify-items: center;
}
}
@media screen and (min-width: 375px)  and (max-width:425px){
}

/* genie effect */

.genie-effect {
  animation: genie 0.8s ease-in-out;
  transform-origin: top left; /* important: start scaling from top-left corner */
}

@keyframes genie {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}