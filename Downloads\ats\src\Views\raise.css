.raise-container {
  max-width: 1200px;
  margin: 5px;
  padding: 5px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.raise-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  padding: 10px;
}

.form-header {
  grid-column: 1 / -1;
  text-align: center;
  margin-bottom: 2px;
}

.form-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
  margin: 0;
}

.form-grid {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 2px;
}

h2 {
  text-align: center;
  margin-bottom: -20px;
  color: #333;
  grid-column: 1 / -1; /* Span across both columns */
}

/* Modern Drag & Drop Zone */
.drop-zone {
  border: 4px dashed #cbd5e1;
  border-radius: 12px;
  padding: 5px;
  text-align: center;
  margin-bottom: 5px;
  background-color: #f8fafc;
  grid-column: 1 / -1;
  transition: all 0.3s ease;
  position: relative;
}

.drop-zone:hover {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.drop-zone.drag-over {
  border-color: #3b82f6;
  background-color: #dbeafe;
  transform: scale(1.02);
}

.drop-zone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b;
}

.upload-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #94a3b8;
}

.drop-text-main {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #475569;
}

.drop-text-sub {
  font-size: 14px;
  margin: 0;
  color: #64748b;
}

/* Image Gallery */
.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 10px;
  grid-column: 1 / -1;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  background: #f1f5f9;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.gallery-item:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.2s ease;
}

.gallery-item:hover .gallery-image {
  opacity: 0.9;
}

.remove-image-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  opacity: 0;
}

.gallery-item:hover .remove-image-btn {
  opacity: 1;
}

.remove-image-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Lightbox */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.lightbox-image {
  max-width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
  position: absolute;
  top: 20px;
  right: 30px;
  background: none;
  border: none;
  color: white;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  padding: 10px;
  line-height: 1;
  transition: color 0.2s ease;
}

.lightbox-close:hover {
  color: #cbd5e1;
}

.preview-image-wrapper {
  position: relative;
  display: inline-block;
  margin: 5px;
}

.preview-img {
  max-width: 200px;
  max-height: 150px;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 2px solid #e0e0e0;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.preview-img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.remove-image-button {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition:
    background-color 0.2s ease,
    transform 0.2s ease;
}

.remove-image-button:hover {
  background: #ff3742;
  transform: scale(1.1);
}

.paste-placeholder {
  color: #666;
  font-size: 16px;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 80px;
}

.paste-placeholder span {
  font-size: 24px;
  margin-bottom: 8px;
}

.form-group {
  margin-bottom: 20px;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  flex-shrink: 0;
}

.form-group input,
.form-group select,
.form-group textarea {
  flex: 1;
}

.raise-button {
  height: 50px;
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  font-weight: 600;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  grid-column: 1 / -1;
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}

.raise-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
  background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
}

.raise-button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.raise-button:disabled:hover {
  transform: none;
  box-shadow: none;
}

h2 {
  font-weight: 600;
  font-size: 2rem;
  margin-top: -1rem;
  margin-bottom: -0.5rem;
}

label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 600;
  font-size: 14px;
  /* text-transform: uppercase; */
  letter-spacing: 0.5px;
}

.form-group-issue input,
.form-group-issue select {
  width: 100%;
  height: 48px; /* Fixed height for consistency */
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background-color: white;
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease;
  box-sizing: border-box; /* Ensure padding is included in height */
  line-height: 1.4;
  appearance: none; 
}

/* Custom dropdown arrow for select elements */
.form-group-issue select {
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px; /* Make room for the arrow */
}

input:focus,
select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Ensure placeholder text is properly styled */
.form-group-issue  input::placeholder {
  color: #95a5a6;
  opacity: 1;
}

/* Fix for any browser-specific height issues */
.form-group-issue
input[type="text"],
.form-group-issue
input[type="email"],
.form-group-issue
input[type="password"],
.form-group-issue
select {
  min-height: 48px;
}

.form-group-issue  textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  grid-column: 1 / -1;
  min-height: 100px;
  font-family: inherit;
  transition:
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

.form-group-issue  textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.view-issues-button-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  padding-right: 20px;
}

.toggle-view-button {
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.toggle-view-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .raise-form {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 5px;
  }

  .form-header h2 {
    font-size: 1.8rem;
  }

  .drop-zone {
    padding: 24px;
    margin-bottom: 20px;
  }

  .upload-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 12px;
  }

  .drop-text-main {
    font-size: 16px;
  }

  .drop-text-sub {
    font-size: 13px;
  }

  .image-gallery {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }

  .view-issues-button-container {
    padding-right: 15px;
  }

  .toggle-view-button {
    padding: 8px 16px;
    font-size: 13px;
  }

  .lightbox-close {
    top: 15px;
    right: 20px;
    font-size: 32px;
  }
}

@media (max-width: 480px) {
  .raise-container {
    margin: 2px;
    padding: 2px;
  }

  .raise-form {
    padding: 10px;
  }

  .form-header h2 {
    font-size: 1.5rem;
  }

  .form-header p {
    font-size: 1rem;
  }

  .drop-zone {
    padding: 20px;
    margin-bottom: 16px;
  }

  .upload-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }

  .drop-text-main {
    font-size: 14px;
  }

  .drop-text-sub {
    font-size: 12px;
  }

  .image-gallery {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }

  input,
  select {
    height: 44px;
    padding: 10px 14px;
    font-size: 13px;
  }

  textarea {
    padding: 10px 14px;
    font-size: 13px;
  }

  .raise-button {
    height: 45px;
    font-size: 14px;
  }

  .lightbox-overlay {
    padding: 10px;
  }

  .lightbox-close {
    top: 10px;
    right: 15px;
    font-size: 28px;
  }
}
