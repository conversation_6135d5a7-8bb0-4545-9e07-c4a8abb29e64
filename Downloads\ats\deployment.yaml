apiVersion: apps/v1
kind: Deployment
metadata:
  name: react2-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: react2
  template:
    metadata:
      labels:
        app: react2
    spec:
      containers:
      - name: react2-container
        image: registry.digitalocean.com/makonis-mti/react2:latest  # Use the full image path
        imagePullPolicy: Always  # Set to Always to pull from the registry
        ports:
        - containerPort: 80
      imagePullSecrets:  # Add this section to use the created secret
      - name: registry-secret  # Name of the secret created
---
apiVersion: v1
kind: Service
metadata:
  name: react2-service
spec:
  selector:
    app: react2
  type: LoadBalancer  # Use LoadBalancer for external access
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
