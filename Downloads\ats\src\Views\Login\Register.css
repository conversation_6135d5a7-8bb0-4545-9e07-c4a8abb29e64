/* Modern Register Page with Split Layout */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Main Register Container with Split Layout */
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: stretch;
  justify-content: stretch;
  position: relative;
  overflow: hidden;
}

/* Left Side Gradient Overlay */
.register-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  z-index: 2;
}

/* Right Side Image Display */
.register-container::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  width: 50%;
  height: 100%;
  background: url('https://plus.unsplash.com/premium_vector-1682298505658-3c147b1222a4?q=80&w=1124&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
  background-size: cover;
  background-position: center;
  z-index: 2;
}

/* Register Content Positioning */
.register-content {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 28rem;
  margin-left: 10%;
  margin-right: auto;
  display: flex;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 0;
}

/* Modern Register Form Container */
.logins {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  padding: 3rem;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideUp 0.6s ease-out;
  width: 100%;
  max-width: 420px;
  position: relative;
  overflow: hidden;
}

/* Subtle background pattern for form */
.logins::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}

/* Ensure form content is above the background pattern */
.logins > * {
  position: relative;
  z-index: 2;
}

/* Animation for form appearance */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Register Title Styling */
h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Form Labels */
label,
span {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modern Input Styling */
#name,
#pass,
#user,
#email {
  width: 100%;
  padding: 0.875rem 1rem;
  margin-bottom: 1rem;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(156, 163, 175, 0.3);
  border-radius: 0.75rem;
  box-sizing: border-box;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

#name:focus,
#pass:focus,
#user:focus,
#email:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* Hide password reveal button */
input[type="password"]::-ms-reveal {
  display: none;
}

/* Modern Submit Button */
input[type="submit"],
button[type="submit"] {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.875rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

input[type="submit"]:hover,
button[type="submit"]:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

input[type="submit"]:active,
button[type="submit"]:active {
  transform: translateY(0);
}

/* Loading state for submit button */
input[type="submit"]:disabled,
button[type="submit"]:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Form field focus effects */
#name:focus,
#pass:focus,
#user:focus,
#email:focus {
  transform: translateY(-1px);
}

/* Subtle animations for better UX */
#name,
#pass,
#user,
#email,
input[type="submit"],
button[type="submit"] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
/* Error Message Styling */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: -0.5rem;
  margin-bottom: 1rem;
  font-weight: 500;
}

/* Password Toggle Icon Styling */
.password-toggle-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  font-size: 1.25rem;
  z-index: 10;
  transition: color 0.3s ease;
}

.password-toggle-icon:hover {
  color: #667eea;
}

/* Input container for password field */
.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .register-container::before,
  .register-container::after {
    width: 100%;
    height: 50%;
  }

  .register-container::before {
    top: 0;
    left: 0;
  }

  .register-container::after {
    bottom: 0;
    right: 0;
    top: 50%;
  }

  .register-content {
    margin-left: 0;
    margin: 0 auto;
    max-width: 90%;
    padding: 2rem;
  }

  .logins {
    padding: 2rem;
    margin: 1rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  label,
  span {
    font-size: 0.8rem;
  }

  #name,
  #pass,
  #user,
  #email {
    font-size: 0.9rem;
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .register-content {
    padding: 1rem;
  }

  .logins {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  h1 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }
}
