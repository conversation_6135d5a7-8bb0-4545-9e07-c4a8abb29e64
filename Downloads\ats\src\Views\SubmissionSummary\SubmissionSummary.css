.select-recruiter-container,
.view-table-container {
  border-radius: 5px;

  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  margin-left: -12px;
}

.button_ss {
  height: 29px;
  width: 160px;
  /* width: 100%; */
  padding: 2px;
  /* border: none;
            outline: none; */
  color: #fff;
  border-radius: 5px;
  background-color: #32406d;
  transition: all 0.3s linear;
  cursor: pointer;
  text-decoration: none;
  margin-right: 5px;
}

/* .button_ss:hover {
            color: #ffffff; 
           
          } */

/* Responsive styles */

@media (max-width: 991px) {
  .section {
    margin-left: 0;
  }

  .sidebar {
    left: -225px;
  }
}

@media (max-width: 767px) {
  .top_navbar {
    padding: 0 15px;
  }

  .top_navbar .heading {
    margin-left: 10px;
  }

  .top_navbar .heading h1 {
    font-size: 16px;
  }

  .container {
    margin-top: 20px;
    /* Adjust margin to avoid overlapping with top navbar */
  }
}

@media (max-width: 575px) {
  .container {
    margin-top: 20px;
    /* Adjust margin to avoid overlapping with top navbar */
  }
  h3 {
    font-size: 20px;
  }

  .logo {
    margin-left: 10px;
  }

  /* h1 {
    } */
}

/* ::-webkit-scrollbar {
  background: rgba(255, 255, 255, 0.25);
}

::-webkit-scrollbar-thumb {
  background-color: #42404034;
  border-radius: 10px;
} */

/* notification Badge */
.badge {
  position: absolute;
  top: -10px;
  right: -2px;
  padding: 5px 10px;
  border-radius: 50%;
  background: red;
  color: white;
}

/* buttons container */
.buttons-container1 {
  margin: 20px 0;
}

/* Filter Container */
.filter-container {
  display: flex;
  flex-wrap: wrap;
  /* margin: 10px 5px; */
  margin-bottom: 10px;
}

.filter-container div {
  display: flex;
  flex-direction: column;
  /* justify-content: center;
    align-items: center;
    flex: 1 1 auto;
    outline: 1px solid black; */
  padding: 3px 0;
  /* max-width: 180px; */
}

.filter-container label {
  font-size: 15px;
  font-weight: 600;
  color: #2e2e2e;
  margin-top: -13px;
  margin-left: -12px;
}

.filter-container input[type="date"],
.filter-container select {
  /* outline: none; */
  font-size: 12px;
  font-weight: 400;
  color: #333;
  border-radius: 5px;
  border: 1px solid #aaa;
  box-sizing: border-box;
  height: 30px;
  padding: 0 5px;
  margin-top: -7px;
  margin-left: -12px;
  width: 149px;
}

@media screen and (min-width: 1024px) {
  .filter-container > div {
    flex-basis: 16.66%;
    /* Each div takes up 1/6 of the container */
    margin-right: 10px;
    /* Maintain spacing between the filter items */
    margin-bottom: 10px;
    /* Maintain spacing between rows */
  }
}

/* For tablet screens */
@media screen and (max-width: 1023px) and (min-width: 768px) {
  .filter-container > div {
    flex-basis: 33.33%;
    /* Each div takes up 1/3 of the container */
    margin-right: 10px;
    /* Maintain spacing between the filter items */
    margin-bottom: 10px;
    /* Maintain spacing between rows */
  }
}

/* For mobile screens */
@media screen and (max-width: 767px) {
  .filter-container,
  .buttons-container1 {
    margin-left: 3px;
    /* flex-direction: column; */
    /* Display the filter items in a single column */
  }

  .buttons-container1 button {
    margin-bottom: 10px;
  }

  .filter-container > div {
    /* flex-basis: 100%; */
    /* Each div takes up the full width of the container */
    margin-bottom: 10px;
    /* Maintain spacing between rows */
  }
}

.view-table-container table {
  width: 100%;
  border-collapse: collapse;
}

.view-table-container table thead {
  font-weight: normal;
  border: none;
  white-space: nowrap;
  background-color: white;
}

.view-table-container table th {
  text-align: left;
  font-size: 13px;
  border: 1px solid black;
  padding: 3px;
}

.view-table-container table td {
  text-align: left;
  border: 1px solid black;
  font-size: 13px;
  padding-left: 2px;
  font-weight: 500;
  color: #2e2e2e;
  padding: 3px;
}

/* Not displaying second row in thead(it is empty) */
.view-table-container table thead tr:nth-child(2) {
  display: none;
}

/* tbody last row */
.view-table-container table tbody tr:last-child {
  font-weight: normal;
  border: none;
  white-space: nowrap;
  /* background-color: white; */
}

/* tbody last column (last td in every row) */
.view-table-container table tbody tr td:last-child {
  font-weight: normal;
  /* border: none; */
  /* white-space: nowrap; */
  /* background-color: white; */
}

/* Thead is fixed when vertical scrolling */
.view-table-container table thead th {
  position: sticky;
  top: 0;
  z-index: 1;
  background: white;
  color: black;
}

/* first cell in both row and column is fixed for both scrolling */
.view-table-container table thead th:first-child {
  position: sticky;
  left: 0px;
  z-index: 99;
}

/* first column in tbody  freezed when horizontal scroll*/
.view-table-container table tbody th {
  position: sticky;
  left: 0;
  background: white;
  z-index: 1;
}

/* hidding download button */
.hidden {
  display: none;
}
.ss_form {
  background: rgba(255, 255, 255, 0.25);
  /* box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    backdrop-filter: blur(11.5px);
    -webkit-backdrop-filter: blur(11.5px);
    border-radius: 10px; */
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 10px;

  border-radius: 5px;
  padding: 20px;
  /* width: 25rem; */
  width: 89%;
  height: 203px;
  margin-top: 20px;
  margin-left: 70px;
}
.submission-table th:first-child,
.submission-table td:first-child {
  position: sticky;
  left: 0;
  background: white;
  z-index: 3;
}

.submission-table th {
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}

.container5{
  display: flex;
  flex-direction: column;
  height: 85vh;
}



.cards{
   
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: black;
  min-width:100px;
  height: 85px;
  margin-right: 20px;
  overflow: hidden;

}

.cards:hover {
  /* box-shadow: 5px 5px 10px #9c39ff; */
  transition: box-shadow 0.1s ease-in;
}

.card-headers {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.4);
}

.card-bodys {
  padding: 10px;
}

.card-footers {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 10px;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.4);
}
.closeModalButton {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 10px 20px;
  background-color: #32406D;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  z-index: 1000;
}
 
.closeModalButton:hover {
  background-color: #32406D;
}
  
canvas {
  width: 50% !important;
  height: 300px !important;
  display: block !important;
  box-sizing: border-box !important;
}

.submissionreport{
   
  display: flex;
  padding-top: 35px;
  text-align: center;
  justify-content: center;
  color: black;
  font-size: large;

}
@media screen and (max-width: 767px) {
  .container5{
    margin-left: -75px !important;
    height: 75vh !important;
  }
  
  .align{
    text-align:left;
  }
  #from_date{
    width: 40% !important;
  }
  #to_date{
    width: 40% !important;
  }
  .view-table-container{
    margin-top: 0px !important;
    height: 300px;
  }
}

@media screen and (min-width:320px) and (max-width:375px){
  #from_date{
    width: 150px !important;
  }
  #to_date{
    width: 150px !important;
  }
  .filter-container{
    justify-content: space-between !important;
  }
  /* .ss_form{
    height: 350px !important;
  } */
  .view-table-container{
    width: 86% !important;
  }
  .container5{
    height: 75vh !important;
  }
}
  @media screen and (min-width:375px) and (max-width:425px){
    #from_date{
      width: 150px !important;
    }
    #to_date{
      width: 150px !important;
    }
    .filter-container{
      justify-content: space-between !important;
    }
    .container5 {
      height: 70vh !important;
    }
  }