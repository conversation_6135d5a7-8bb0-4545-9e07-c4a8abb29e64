// candidateSlice.js
import { createSlice } from '@reduxjs/toolkit';

const candidateSlice = createSlice({
  name: 'candidates',
  initialState: {
    data: [],
  },
  reducers: {
    setCandidates(state, action) {
      state.data = action.payload;
    },
    clearCandidates(state) {
      state.data = []; // ✅ this clears candidates
    },
  },
});

export const { setCandidates,clearCandidates  } = candidateSlice.actions;
export default candidateSlice.reducer;
